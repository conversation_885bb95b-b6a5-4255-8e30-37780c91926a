# CAP理论

## 核心概念
CAP理论由Eric Brewer在2000年提出，指出分布式系统无法同时满足一致性、可用性和分区容忍性三个特性。

## 三大特性

### 1. 一致性（Consistency）
- **定义**：所有节点同时看到相同数据
- **要求**：任何读操作返回最新写操作结果
- **代价**：可能影响性能和可用性

### 2. 可用性（Availability）
- **定义**：系统持续提供服务，即使部分节点失效
- **要求**：任何时候都能处理请求并返回结果
- **代价**：可能返回过时数据

### 3. 分区容忍性（Partition Tolerance）
- **定义**：网络分区时系统继续运行
- **要求**：节点间通信中断时仍能提供服务
- **必要性**：分布式系统必须具备的特性

## 三选二原则

### 1. CP（一致性 + 分区容忍性）
- **特点**：保证数据一致性，牺牲可用性
- **典型应用**：ZooKeeper、HBase、MongoDB
- **适用场景**：金融系统、配置中心、分布式锁

### 2. AP（可用性 + 分区容忍性）
- **特点**：保证高可用性，牺牲强一致性
- **典型应用**：Cassandra、DynamoDB、CouchDB
- **适用场景**：社交媒体、内容分发、日志收集

### 3. CA（一致性 + 可用性）
- **特点**：理论上不可能，网络分区不可避免
- **现实**：单机系统或局域网系统

## BASE理论

### 基本概念
- **Basically Available**：基本可用，允许损失部分可用性
- **Soft State**：软状态，允许数据暂时不一致
- **Eventually Consistent**：最终一致性，系统最终会达到一致

### 与CAP关系
BASE理论是对CAP理论中AP选择的具体实现指导，强调最终一致性而非强一致性。

## 实际应用选择

### 选择CP的场景
- **金融交易**：数据一致性至关重要，不能有误差
- **配置管理**：配置信息必须一致，避免系统异常
- **分布式锁**：互斥性要求强一致，防止资源冲突

### 选择AP的场景
- **社交网络**：用户体验优先，短暂不一致可接受
- **内容分发**：可用性比一致性重要，允许缓存延迟
- **日志收集**：允许短暂数据丢失，重点保证服务可用

## 常见面试问题

### 1. 基础概念
- **Q: CAP理论是什么？**
- A: 分布式系统无法同时满足一致性、可用性和分区容忍性，只能选择其中两个

- **Q: 为什么不能同时满足三个特性？**
- A: 网络分区不可避免，发生分区时必须在一致性和可用性之间选择

### 2. 实际应用
- **Q: 如何选择CP还是AP？**
- A: 根据业务需求，金融等强一致性场景选CP，社交等用户体验优先选AP

- **Q: BASE理论与CAP的关系？**
- A: BASE是AP选择的实现指导，通过最终一致性平衡可用性和一致性

### 3. 深入理解
- **Q: 微服务架构如何应对CAP？**
- A: 不同服务根据业务特点选择不同策略，核心服务选CP，边缘服务选AP

- **Q: 如何实现最终一致性？**
- A: 通过事件驱动、补偿机制、定时同步等方式保证数据最终一致