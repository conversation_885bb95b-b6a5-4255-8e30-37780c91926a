# 分布式事务

## 核心概念

分布式事务是指跨越多个数据库或服务的事务，需要保证ACID特性，特别是原子性和一致性。

## 主要实现方案

### 1. 两阶段提交（2PC）

**工作原理**：
- **准备阶段**：协调者询问所有参与者是否可以提交
- **提交阶段**：如果都同意则提交，否则回滚

**优缺点**：
- ✅ 强一致性，保证原子性
- ❌ 阻塞问题，协调者单点故障

### 2. 三阶段提交（3PC）

**工作原理**：
- **CanCommit**：询问是否可以提交
- **PreCommit**：预提交，锁定资源
- **DoCommit**：最终提交

**优缺点**：
- ✅ 减少阻塞时间
- ❌ 实现复杂，网络分区问题

### 3. TCC模式

**工作原理**：
- **Try**：尝试执行并预留资源
- **Confirm**：确认执行
- **Cancel**：取消并释放资源

**优缺点**：
- ✅ 性能好，无长时间锁定
- ❌ 业务侵入性强，实现复杂

### 4. Saga模式

**工作原理**：
- **正向操作**：按顺序执行各个服务的操作
- **补偿操作**：失败时执行反向补偿

**优缺点**：
- ✅ 适合长事务，性能好
- ❌ 最终一致性，补偿逻辑复杂

### 5. 消息队列最终一致性

**工作原理**：
- **事件驱动**：通过消息队列异步处理
- **重试机制**：失败时自动重试

**优缺点**：
- ✅ 高性能，高可用
- ❌ 最终一致性，调试困难

## 方案选择指南

| 方案 | 一致性 | 性能 | 复杂度 | 适用场景 |
|------|--------|------|--------|----------|
| 2PC | 强一致 | 低 | 中 | 短事务，强一致性要求 |
| 3PC | 强一致 | 低 | 高 | 网络稳定环境 |
| TCC | 强一致 | 高 | 高 | 核心业务，性能要求高 |
| Saga | 最终一致 | 高 | 中 | 长事务，微服务架构 |
| 消息队列 | 最终一致 | 很高 | 低 | 高并发，可容忍延迟 |

## 常见面试问题

### 1. 基础概念
- **Q: 什么是分布式事务？**
- A: 跨越多个数据库或服务的事务，需要保证ACID特性

- **Q: 分布式事务的主要挑战？**
- A: 网络延迟、节点故障、数据一致性、性能问题

### 2. 实现方案
- **Q: 2PC和3PC的区别？**
- A: 3PC增加了预提交阶段，减少阻塞时间但增加了复杂度

- **Q: TCC模式的优势？**
- A: 无长时间锁定，性能好，但需要业务层实现Try/Confirm/Cancel

### 3. 实际应用
- **Q: 微服务架构下如何选择分布式事务方案？**
- A: 根据一致性要求和性能需求选择，通常推荐Saga或消息队列

- **Q: 如何处理分布式事务的幂等性？**
- A: 使用唯一标识符、状态检查、数据库约束等方式保证幂等