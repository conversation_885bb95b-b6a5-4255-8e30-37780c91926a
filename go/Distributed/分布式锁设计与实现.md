# 分布式锁设计与实现

## 基本概念

### 1. 什么是分布式锁
在分布式系统中，用于控制多个节点对共享资源的并发访问，确保同一时间只有一个节点可以操作资源。

### 2. 应用场景
- **防止重复执行**：定时任务、消息消费
- **资源互斥访问**：库存扣减、账户余额
- **数据一致性**：缓存更新、配置修改

## 实现方案

### 1. 基于Redis的分布式锁

#### 基本实现
- **加锁**：`SET key value NX PX expire_time`
- **解锁**：Lua脚本保证原子性
- **优点**：性能高、实现简单
- **缺点**：单点故障风险

#### Redlock算法
- **原理**：在多个Redis实例上获取锁
- **要求**：大多数实例(N/2+1)获取成功
- **步骤**：
  1. 记录开始时间
  2. 依次向所有实例申请锁
  3. 计算获取锁的总时间
  4. 判断是否获取成功

### 2. 基于ZooKeeper的分布式锁

#### 实现原理
- **临时顺序节点**：创建临时顺序节点
- **最小节点获锁**：序号最小的节点获得锁
- **监听机制**：监听前一个节点的删除事件
- **优点**：强一致性、公平锁
- **缺点**：性能相对较低

#### 实现步骤
1. 创建临时顺序节点
2. 获取所有子节点并排序
3. 判断是否为最小节点
4. 不是则监听前一个节点

### 3. 基于数据库的分布式锁

#### 实现方式
- **唯一约束**：利用数据库唯一索引
- **乐观锁**：版本号机制
- **悲观锁**：SELECT FOR UPDATE
- **优点**：强一致性、事务支持
- **缺点**：性能较差、死锁风险

## 关键问题解决

### 1. 死锁预防
- **超时机制**：设置锁的过期时间
- **心跳续约**：定期延长锁的有效期
- **异常处理**：进程崩溃时自动释放锁

### 2. 锁的安全性
- **唯一标识**：使用UUID标识锁的持有者
- **原子操作**：加锁和解锁的原子性
- **误删防护**：验证锁的持有者身份

### 3. 性能优化
- **锁粒度**：细化锁的范围
- **本地缓存**：减少分布式锁的使用
- **异步处理**：非关键路径异步执行

## 各方案对比

### 性能对比
- **Redis**：性能最高，毫秒级响应
- **ZooKeeper**：性能中等，网络开销较大
- **数据库**：性能最低，受限于数据库性能

### 可靠性对比
- **Redis**：单点故障风险，Redlock提高可靠性
- **ZooKeeper**：强一致性，集群容错
- **数据库**：事务保证，但可能死锁

### 复杂度对比
- **Redis**：实现简单，但需考虑边界情况
- **ZooKeeper**：实现复杂，但框架支持好
- **数据库**：实现简单，但性能限制

## 最佳实践

### 1. 选择原则
- **高性能场景**：选择Redis
- **强一致性场景**：选择ZooKeeper
- **简单场景**：选择数据库

### 2. 设计要点
- **合理的超时时间**：避免死锁和误释放
- **重试机制**：获取锁失败时的重试策略
- **监控告警**：锁的使用情况和异常监控

### 3. 常见陷阱
- **时钟漂移**：不同机器时钟不同步
- **网络分区**：网络故障导致的锁失效
- **GC停顿**：长时间GC导致锁超时

## 常见面试问题

### 1. 基础概念
- **Q: 什么是分布式锁？为什么需要？**
- A: 在分布式环境下控制多个节点对共享资源的并发访问，防止数据不一致

- **Q: 分布式锁的实现方案有哪些？**
- A: 主要有Redis、ZooKeeper、数据库三种方案

### 2. 实现原理
- **Q: Redis分布式锁如何实现？**
- A: 使用SET key value NX PX expire_time加锁，Lua脚本原子性解锁

- **Q: ZooKeeper分布式锁的原理？**
- A: 创建临时顺序节点，序号最小的获得锁，监听前一个节点删除事件

- **Q: Redlock算法是什么？**
- A: 在多个Redis实例上获取锁，大多数(N/2+1)成功才算获锁成功

### 3. 关键问题
- **Q: 如何防止分布式锁死锁？**
- A: 设置超时时间、心跳续约机制、异常时自动释放

- **Q: 如何保证锁的安全性？**
- A: 使用唯一标识(UUID)、原子操作、验证锁持有者身份

- **Q: 分布式锁的性能优化方法？**
- A: 细化锁粒度、减少锁使用、本地缓存、异步处理

### 4. 方案选择
- **Q: 如何选择分布式锁方案？**
- A: 高性能选Redis，强一致性选ZooKeeper，简单场景选数据库

- **Q: 分布式锁常见问题及解决方案？**
- A: 时钟漂移(使用相对时间)、网络分区(多数派机制)、GC停顿(合理超时时间)

### 5. 实际应用
- **Q: 分布式锁的典型应用场景？**
- A: 防止重复执行(定时任务)、资源互斥访问(库存扣减)、数据一致性(缓存更新)
