# Raft分布式共识算法

## 基本概念

Raft是一种易于理解的分布式共识算法，用于管理分布式系统中的状态一致性。

### 节点角色
- **Leader（领导者）**：处理客户端请求，负责日志复制
- **Follower（跟随者）**：被动接收Leader的日志条目
- **Candidate（候选者）**：Leader选举时的临时状态

### 核心特性
- **强一致性**：保证所有节点状态一致
- **容错性**：容忍少于半数节点故障
- **简单性**：相比Paxos更易理解和实现

## 核心机制

### 1. Leader选举

**选举流程**：
1. **触发条件**：Follower超时未收到心跳，转为Candidate
2. **投票过程**：增加term，向其他节点请求投票
3. **成为Leader**：获得多数票成为Leader，开始发送心跳

**选举规则**：
- 每个term最多投票一次
- 只投票给日志至少和自己一样新的候选者
- 随机超时时间避免选举冲突

### 2. 日志复制

**复制流程**：
1. **接收请求**：Leader接收客户端请求，追加到本地日志
2. **并行复制**：并行发送AppendEntries RPC到所有Follower
3. **提交确认**：收到多数确认后提交日志条目

**一致性保证**：
- 日志匹配特性：相同index和term的日志条目内容相同
- Leader完整性：只有拥有最新日志的节点才能成为Leader
- 状态机安全性：已提交的日志条目不会被覆盖

### 3. 安全性保证

**Term机制**：
- 每次选举term递增，作为逻辑时钟
- 节点只能在每个term投票一次
- 高term的消息会被优先处理

**Leader完整性**：
- 只有拥有最新日志的节点才能成为Leader
- 通过比较lastLogIndex和lastLogTerm确定

**提交规则**：
- 只有当前term的日志条目才能被提交
- 提交时需要大多数节点确认

## 容错性分析

### 故障类型与处理
- **节点崩溃**：Leader重新选举，Follower重启后同步日志
- **网络分区**：多数派继续服务，少数派停止服务
- **消息丢失**：重试机制和心跳检测保证

### 容错能力
- **2f+1个节点**：最多容忍f个节点故障
- **例子**：5节点集群可容忍2个节点故障，仍能正常工作

## 性能优化

### 主要优化策略
- **批量处理**：批量发送日志条目，减少网络开销
- **Pipeline复制**：并行发送多个AppendEntries，提高吞吐量
- **预投票**：减少不必要的选举，提高稳定性
- **日志压缩**：定期压缩日志，减少存储空间

## 实际应用

### 典型应用场景
- **etcd**：Kubernetes配置存储，保证强一致性
- **Consul**：服务发现和配置管理，Leader选举
- **TiKV**：分布式KV存储，Multi-Raft架构
- **ClickHouse**：分布式数据库的元数据管理

## 常见面试问题

### 1. 基础概念
- **Q: Raft算法的三个角色是什么？**
- A: Leader（领导者）、Follower（跟随者）、Candidate（候选者）

- **Q: Raft如何保证只有一个Leader？**
- A: 通过term机制和多数票选举，每个term最多一个Leader

### 2. 核心机制
- **Q: Raft的选举过程是怎样的？**
- A: Follower超时→转为Candidate→增加term并投票→获得多数票成为Leader

- **Q: 如何保证日志一致性？**
- A: 通过日志匹配特性和Leader完整性，确保已提交日志不被覆盖

### 3. 容错与优化
- **Q: Raft最多能容忍多少节点故障？**
- A: 2f+1个节点最多容忍f个故障，如5节点容忍2个故障

- **Q: Raft相比Paxos有什么优势？**
- A: 更易理解和实现，有强Leader，日志结构更简单